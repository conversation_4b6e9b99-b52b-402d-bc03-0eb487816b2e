package com.iflytek.lynxiao.asset.action.doc.dedup.service;

import com.iflytek.lynxiao.asset.action.doc.dedup.config.DocDedupProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.CacheMongoTemplate;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.bson.Document;
import skynet.boot.pandora.support.TaskCancelCache;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Indexes.ascending;

/**
 * GID选择服务
 * 职责：从MongoDB中筛选出重复的GID并存储到Redis缓存
 *
 * <AUTHOR> by architect
 */
@Slf4j
public class GidSelectionService implements AutoCloseable {

    private static final String SPLIT_FIELD = "_id";
    
    private final DocDedupProperties dedupProperties;
    private final BucketCacheService bucketCacheService;
    private final TaskCancelCache taskCancelCache;
    private final MqMessageCtrlSender mqMessageCtrlSender;
    private final GidCache gidCache;
    private final ExecutorService partitionExecutor;

    public GidSelectionService(DocDedupProperties dedupProperties,
                             BucketCacheService bucketCacheService,
                             TaskCancelCache taskCancelCache,
                             MqMessageCtrlSender mqMessageCtrlSender,
                             GidCache gidCache) {
        this.dedupProperties = dedupProperties;
        this.bucketCacheService = bucketCacheService;
        this.taskCancelCache = taskCancelCache;
        this.mqMessageCtrlSender = mqMessageCtrlSender;
        this.gidCache = gidCache;

        // 创建分区处理线程池
        int poolSize = dedupProperties.getTaskConfig().getPartition();
        this.partitionExecutor = new ThreadPoolExecutor(
                poolSize,
                poolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new BasicThreadFactory.Builder().namingPattern("dedup-select-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy() // 使用调用者运行策略，避免任务丢失
        );
    }

    /**
     * 选择重复的GID
     */
    public long selectDuplicateGids(String taskId, String bucketCode) throws Exception {
        long startTime = System.currentTimeMillis();

        // 第一步：获取bucketCode库的总数（使用mongo预估值）
        long totalDocumentCount = getTotalDocumentCount(bucketCode);
        log.info("Total document count for bucketCode={}: {}", bucketCode, totalDocumentCount);

        // 计算分片数
        int shardCount = calculateShardCount(totalDocumentCount);
        log.info("Calculated shard count for taskId={}: {}", taskId, shardCount);

        try (GidFilterPipelineHandler pipelineHandler = createPipelineHandler(taskId, shardCount)) {
            pipelineHandler.init(dedupProperties.getTaskConfig().getPipelineQueueSize(), "");

            // 扫描文档并处理
            scanDocuments(taskId, bucketCode, pipelineHandler);

            // 等待管道处理完毕
            waitForPipelineCompletion(pipelineHandler);

            long cost = System.currentTimeMillis() - startTime;
            long gidTotal = pipelineHandler.getGidTotal();

            log.info("GID selection completed. taskId={}, bucketCode={}, gidTotal={}, cost={}ms",
                    taskId, bucketCode, gidTotal, cost);

            return gidTotal;
        }
    }

    /**
     * 获取bucketCode库的总文档数（使用mongo预估值）
     */
    private long getTotalDocumentCount(String bucketCode) {
        try {
            CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
            MongoCollection<Document> collection = template.getMongoTemplate().getCollection(bucketCode);

            // 使用estimatedDocumentCount获取预估值，比count()更快
            long estimatedCount = collection.estimatedDocumentCount();

            log.info("Estimated document count for bucketCode={}: {}", bucketCode, estimatedCount);
            return estimatedCount;
        } catch (Exception e) {
            log.error("Failed to get document count for bucketCode={}", bucketCode, e);
            // 如果预估失败，使用精确计数作为备选方案
            try {
                CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
                long exactCount = template.count(new Query(), bucketCode);
                log.warn("Using exact count as fallback for bucketCode={}: {}", bucketCode, exactCount);
                return exactCount;
            } catch (Exception ex) {
                log.error("Failed to get exact count for bucketCode={}", bucketCode, ex);
                return 0;
            }
        }
    }

    /**
     * 计算分片数
     */
    private int calculateShardCount(long totalDocumentCount) {
        if (totalDocumentCount <= 0) {
            return 1;
        }

        int gidsPerShard = dedupProperties.getTaskConfig().getGidsPerShard();
        int shardCount = (int) Math.ceil((double) totalDocumentCount / gidsPerShard);

        // 确保至少有1个分片，最多不超过1000个分片（避免过度分片）
        return Math.max(1, Math.min(shardCount, 1000));
    }

    /**
     * 获取分片数量（公开方法供外部调用）
     */
    public int getShardCount(String bucketCode) {
        long totalDocumentCount = getTotalDocumentCount(bucketCode);
        return calculateShardCount(totalDocumentCount);
    }

    /**
     * 创建管道处理器
     */
    private GidFilterPipelineHandler createPipelineHandler(String taskId, int shardCount) {
        return new GidFilterPipelineHandler(
            taskId,
            mqMessageCtrlSender,
            dedupProperties.getTaskConfig(),
            gidCache,
            shardCount
        );
    }

    /**
     * 扫描文档
     */
    private void scanDocuments(String taskId, String bucketCode, GidFilterPipelineHandler pipelineHandler)
            throws InterruptedException {
        long startTime = System.currentTimeMillis();

        CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
        MongoCollection<Document> collection = template.getMongoTemplate().getCollection(bucketCode);

        // 检查集合是否存在数据
        long documentCount = collection.countDocuments();
        if (documentCount == 0) {
            log.warn("Collection is empty, no documents to process. taskId={}, bucketCode={}, documentCount={}",
                    taskId, bucketCode, documentCount);
            log.info("Document scanning completed (empty collection). taskId={}, bucketCode={}, cost={}ms",
                    taskId, bucketCode, System.currentTimeMillis() - startTime);
            return;
        }

        // 获取ID范围
        Document minDoc = collection.find().sort(new Document(SPLIT_FIELD, 1)).first();
        Document maxDoc = collection.find().sort(new Document(SPLIT_FIELD, -1)).first();

        if (minDoc == null || maxDoc == null) {
            log.warn("Unable to find min/max documents despite non-zero count. taskId={}, bucketCode={}, documentCount={}",
                    taskId, bucketCode, documentCount);
            return;
        }

        long minId = minDoc.getLong(SPLIT_FIELD);
        long maxId = maxDoc.getLong(SPLIT_FIELD);

        log.info("Document ID range: minId={}, maxId={}, bucketCode={}", minId, maxId, bucketCode);

        // 创建分区任务并提交
        int partition = dedupProperties.getTaskConfig().getPartition();
        long range = (maxId - minId) / partition;

        if (range == 0) {
            partition = 1;
            range = maxId - minId;
        }

        CountDownLatch countDownLatch = new CountDownLatch(partition);

        for (int i = 0; i < partition; i++) {
            long startId = minId + i * range;
            long endId = (i == partition - 1) ? maxId : (startId + range) - 1;

            partitionExecutor.submit(() ->
                processPartition(taskId, bucketCode, startId, endId, pipelineHandler, countDownLatch)
            );
        }

        // 等待所有分区处理完成
        boolean completed = countDownLatch.await(
            dedupProperties.getTaskConfig().getQueryDocTimeout().toMinutes(),
            TimeUnit.MINUTES
        );

        if (!completed) {
            log.warn("Document scanning timeout. taskId={}, bucketCode={}", taskId, bucketCode);
        }

        log.info("Document scanning completed. taskId={}, bucketCode={}, cost={}ms",
                taskId, bucketCode, System.currentTimeMillis() - startTime);
    }



    /**
     * 处理单个分区
     */
    private void processPartition(String taskId, String bucketCode, long startId, long endId,
                                GidFilterPipelineHandler pipelineHandler, CountDownLatch countDownLatch) {
        log.info("Processing partition: taskId={}, startId={}, endId={}", taskId, startId, endId);

        try {
            CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
            MongoCollection<Document> collection = template.getMongoTemplate().getCollection(bucketCode);

            // 统计分区数据量
            // countPartitionData(pipelineHandler, collection, startId, endId);

            // 处理分区数据
            processPartitionData(taskId, collection, pipelineHandler, startId, endId);

        } catch (Exception e) {
            log.error("Partition processing failed: taskId={}, startId={}, endId={}",
                    taskId, startId, endId, e);
            throw new RuntimeException("Partition processing failed", e);
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * 统计分区数据量
     */
    private void countPartitionData(GidFilterPipelineHandler pipelineHandler,
                                  MongoCollection<Document> collection, long startId, long endId) {
        long startTime = System.currentTimeMillis();
        Document filter = new Document(SPLIT_FIELD,
            new Document("$gte", startId).append("$lte", endId));

        long total = collection.countDocuments(filter);
        pipelineHandler.increaseTotal(total);

        log.info("Partition data count: total={}, startId={}, endId={}, cost={}ms",
                total, startId, endId, System.currentTimeMillis() - startTime);
    }

    /**
     * 处理分区数据 - 简化版本
     */
    private void processPartitionData(String taskId, MongoCollection<Document> collection,
                                    GidFilterPipelineHandler pipelineHandler, long startId, long endId)
                                    throws InterruptedException {
        long currentId = startId;
        long totalCount = 0;
        int pageSize = this.dedupProperties.getTaskConfig().getPageSize();

        while (currentId <= endId && !this.taskCancelCache.isCancel(taskId)) {
            List<String> gidBatch = new ArrayList<>(pageSize);

            try (MongoCursor<Document> cursor = collection.find(
                    new Document(SPLIT_FIELD, new Document("$gte", currentId).append("$lte", endId)))
                    .projection(new Document(SPLIT_FIELD, 1).append("gid", 1))
                    .sort(ascending(SPLIT_FIELD))
                    .limit(pageSize)
                    .iterator()) {

                while (cursor.hasNext()) {
                    Document record = cursor.next();
                    gidBatch.add(record.getString("gid"));
                    currentId = record.getLong(SPLIT_FIELD);
                }
            }

            // 没有数据则退出
            if (gidBatch.isEmpty()) {
                break;
            }

            // 发送数据到管道
            applyFlowControl(pipelineHandler);
            pipelineHandler.onData(gidBatch);

            totalCount += gidBatch.size();
            currentId++; // 下次查询从下一个ID开始

            log.debug("Processed batch: size={}, currentId={}, totalCount={}",
                    gidBatch.size(), currentId, totalCount);
        }

        log.info("Partition processing completed: startId={}, endId={}, totalProcessed={}",
                startId, endId, totalCount);
    }

    /**
     * 流量控制
     */
    private void applyFlowControl(GidFilterPipelineHandler pipelineHandler) throws InterruptedException {
        int writeLimit = dedupProperties.getTaskConfig().getPipelineWriteLimit();
        if (pipelineHandler.getTodoSize() > writeLimit) {
            long waitTime = dedupProperties.getTaskConfig().getPipelineWriteWait().toMillis();
            log.debug("Pipeline write limit reached. todoSize={}, limit={}, waiting {}ms",
                    pipelineHandler.getTodoSize(), writeLimit, waitTime);
            Thread.sleep(waitTime);
        }
    }

    /**
     * 等待管道处理完成
     */
    private void waitForPipelineCompletion(GidFilterPipelineHandler pipelineHandler) throws InterruptedException {
        log.info("Waiting for pipeline completion. todoSize={}", pipelineHandler.getTodoSize());
        
        while (pipelineHandler.getTodoSize() > 0) {
            TimeUnit.MILLISECONDS.sleep(10);
        }
        
        log.info("Pipeline processing completed");
    }

    @Override
    public void close() throws Exception {
        log.info("Shutting down GidSelectionService");

        // 关闭分区处理线程池
        if (partitionExecutor != null && !partitionExecutor.isShutdown()) {
            partitionExecutor.shutdown();
            try {
                if (!partitionExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Partition executor did not terminate gracefully, forcing shutdown");
                    partitionExecutor.shutdownNow();

                    if (!partitionExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.error("Partition executor did not terminate after forced shutdown");
                    }
                }
            } catch (InterruptedException e) {
                log.warn("Interrupted while shutting down partition executor");
                partitionExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("GidSelectionService shutdown completed");
    }


}
