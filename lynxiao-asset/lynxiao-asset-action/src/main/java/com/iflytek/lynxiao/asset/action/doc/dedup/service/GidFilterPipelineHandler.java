package com.iflytek.lynxiao.asset.action.doc.dedup.service;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.iflytek.lynxiao.asset.action.doc.dedup.config.DocDedupProperties;
import com.iflytek.lynxiao.data.constant.DedupType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import skynet.boot.common.AsyncPipelineHandler;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 将重复的gid发送到redis存储
 *
 * <AUTHOR>  2024/10/24 17:35
 */
@Slf4j
public class GidFilterPipelineHandler extends AsyncPipelineHandler<List<String>, Object> {

    private final String taskId;
    private final MqMessageCtrlSender messageCtrlSender;
    private final DocDedupProperties.DedupTaskConfig dedupTaskConfig;
    private final GidCache gidCache;
    private final BloomFilter<String> bloomFilter;
    private final List<String> gidDataList = new ArrayList<>();


    /**
     * 统计需要处理的总数据量
     */
    private final AtomicLong total = new AtomicLong();

    /**
     * 已经同步到ack的总数值
     */
    private long ackTotal;

    /**
     * 当前已经处理的数据量
     */
    private long done = 0;

    private long failed = 0;

    /**
     * gid 近似值（布隆过滤器假阳性）
     */
    private AtomicLong gidTotal = new AtomicLong(0);

    /**
     * 上次统计进度的时间戳
     */
    private long lastStatisticsTime;

    /**
     * 分片数量
     */
    private final int shardCount;

    public GidFilterPipelineHandler(String taskId, MqMessageCtrlSender messageCtrlSender, DocDedupProperties.DedupTaskConfig dedupTaskConfig, GidCache gidCache, int shardCount) {
        this.taskId = taskId;
        this.messageCtrlSender = messageCtrlSender;
        this.dedupTaskConfig = dedupTaskConfig;
        this.bloomFilter = BloomFilter.create(Funnels.stringFunnel(Charset.defaultCharset()), dedupTaskConfig.getExpectedInsertions(), dedupTaskConfig.getFalsePositiveProbability());
        this.gidCache = gidCache;
        this.shardCount = shardCount;
        this.lastStatisticsTime = System.currentTimeMillis();
    }


    @Override
    protected void onInit(Object param) throws Exception {
        log.info("onInit. param={}", param);
    }

    // 异步处理
    @Override
    public void onEvent(List<String> gids) throws Exception {
        if (CollectionUtils.isEmpty(gids)) {
            return;
        }

        this.gidDataList.clear();
        // 布隆过滤
        for (String gid : gids) {
            log.trace("gid={}", gid);
            if (!StringUtils.hasText(gid)) {
                log.info("gid is empty. skip bloomFilter");
                continue;
            }
            if (this.bloomFilter.mightContain(gid)) {
                this.gidDataList.add(gid);
                this.gidTotal.addAndGet(1);
            } else {
                this.bloomFilter.put(gid);
            }
        }

        this.failed = 0;
        if (!this.gidDataList.isEmpty()) {
            // 存储重复gid到分片
            long start = System.currentTimeMillis();
            try {
                this.gidCache.addBatchWithSharding(taskId, this.gidDataList, shardCount);
            } catch (Exception e) {
                log.error("add gid cache error", e);
                this.failed = gids.size();
            }
            log.debug("send gid to redis cache with sharding. size={}， shardCount={}, cost={}",
                    this.gidDataList.size(), shardCount, System.currentTimeMillis() - start);
        }

        // 发送进度
        messageCtrlSender.sendDone(DedupType.STEP_ONE, taskId, gids.size());
        if (this.failed > 0) {
            messageCtrlSender.sendFail(DedupType.STEP_ONE, taskId, this.failed);
        }
    }


    /**
     * 统计总记录数，每个线程的总和
     *
     * @param totalPerPartition 单个线程处理的总数
     */
    public void increaseTotal(long totalPerPartition) {
        this.total.getAndAdd(totalPerPartition);
    }

    public long getTotal() {
        return this.total.get();
    }

    public long getGidTotal() {
        return this.gidTotal.get();
    }

    public long getDone() {
        return this.done;
    }


}
