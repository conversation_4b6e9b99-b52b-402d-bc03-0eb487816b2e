package com.iflytek.lynxiao.asset.action.doc.dedup.service;

import com.iflytek.lynxiao.asset.action.doc.dedup.config.DocDedupProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;


/**
 * GID缓存服务 - 优化版本
 * 职责：高效存储和检索重复的GID数据，使用Redis Pipeline提升性能
 *
 * <AUTHOR> by architect
 */
@Slf4j
public class GidCache {

    private final DocDedupProperties properties;
    private final RedisTemplate<String, String> redisTemplate;
    private final StringRedisSerializer serializer = new StringRedisSerializer();
    private final GenericToStringSerializer<Long> longSerializer = new GenericToStringSerializer<>(Long.class);

    // 使用Lua脚本确保原子性操作
    private static final String SET_AND_EXPIRE_SCRIPT =
            "redis.call('SET', KEYS[1], 1) " +
                    "redis.call('EXPIRE', KEYS[1], ARGV[1]) " +
                    "return 1";

    // 分片存储的Lua脚本，确保SADD和EXPIRE的原子性
    private static final String SADD_AND_EXPIRE_SCRIPT =
            "redis.call('SADD', KEYS[1], unpack(ARGV, 1, #ARGV-1)) " +
                    "redis.call('EXPIRE', KEYS[1], ARGV[#ARGV]) " +
                    "return 1";

    @Autowired
    public GidCache(DocDedupProperties properties, @Qualifier("stringRedisTemplate") RedisTemplate<String, String> redisTemplate) {
        this.properties = properties;
        this.redisTemplate = redisTemplate;
    }

    public void addBatch(String taskId, List<String> gidDataList) {
        if (CollectionUtils.isEmpty(gidDataList)) {
            return;
        }

        // 使用 Redis 的管道进行批量插入
        for (String gid : gidDataList) {
            String gidKey = "task:" + taskId + ":gid:" + gid;
            redisTemplate.execute((RedisCallback<Object>) connection ->
                    connection.eval(SET_AND_EXPIRE_SCRIPT.getBytes(), ReturnType.INTEGER, 1,
                            serializer.serialize(gidKey),
                            longSerializer.serialize(properties.getTaskConfig().getGidTtl().getSeconds()))
            );
        }
    }

    /**
     * 分片存储GID批次（重载方法）
     * 每个gid根据hash值路由到对应分片，使用set存储
     */
    public void addBatch(String taskId, List<String> gidDataList, int shardCount) {
        if (CollectionUtils.isEmpty(gidDataList)) {
            return;
        }

        // 按分片分组GID
        Map<Integer, Set<String>> shardedGids = new HashMap<>();
        for (String gid : gidDataList) {
            int shardIndex = Math.abs(gid.hashCode()) % shardCount;
            shardedGids.computeIfAbsent(shardIndex, k -> new HashSet<>()).add(gid);
        }

        // 批量存储到各个分片
        long ttlSeconds = properties.getTaskConfig().getGidTtl().getSeconds();
        for (Map.Entry<Integer, Set<String>> entry : shardedGids.entrySet()) {
            int shardIndex = entry.getKey();
            Set<String> gids = entry.getValue();

            String shardKey = "task:" + taskId + ":" + shardIndex;

            // 使用Lua脚本确保SADD和EXPIRE的原子性
            redisTemplate.execute((RedisCallback<Object>) connection -> {
                // 准备所有参数：GID列表 + TTL
                List<byte[]> allArgs = new ArrayList<>();
                for (String gid : gids) {
                    allArgs.add(serializer.serialize(gid));
                }
                allArgs.add(longSerializer.serialize(ttlSeconds));

                // 执行Lua脚本，直接传递可变参数
                return connection.eval(SADD_AND_EXPIRE_SCRIPT.getBytes(), ReturnType.INTEGER, 1,
                        serializer.serialize(shardKey),
                        allArgs.toArray(new byte[0][]));
            });

            log.debug("Added {} gids to shard {} for taskId={}", gids.size(), shardIndex, taskId);
        }
    }

    public void scan(String taskId, Consumer<List<String>> consumer) {
        String pattern = "task:" + taskId + ":gid:*";
        int batchSize = this.properties.getTaskConfig().getGidScanPageSize() < 0 ? 500 : this.properties.getTaskConfig().getGidScanPageSize();
        ScanOptions options = ScanOptions.scanOptions()
                .match(pattern)
                .count(batchSize)
                .build();

        // 扫描redis
        redisTemplate.execute((RedisCallback<Void>) connection -> {
            List<String> batch = new ArrayList<>(batchSize);
            try (Cursor<byte[]> cursor = connection.scan(options)) {
                while (cursor.hasNext()) {
                    String gidKey = new String(cursor.next());
                    String gid = gidKey.substring(gidKey.lastIndexOf(":") + 1);
                    batch.add(gid);

                    // 每当收集到500个gid时，将其传递给consumer并清空batch
                    if (batch.size() == batchSize) {
                        // 传递副本以避免后续修改
                        consumer.accept(new ArrayList<>(batch));
                        batch.clear();
                    }
                }
                // 处理最后一个不足500个的批次
                if (!batch.isEmpty()) {
                    consumer.accept(new ArrayList<>(batch));
                }
            } catch (Exception e) {
                throw new RuntimeException("Error scanning Redis keys", e);
            }
            return null;
        });
    }

    /**
     * 分片扫描GID
     * 直接for循环分片处理，处理完分片直接删除redis key
     */
    public void scanShards(String taskId, int shardCount, Consumer<List<String>> consumer) {
        int batchSize = this.properties.getTaskConfig().getGidScanPageSize() < 0 ? 500 : this.properties.getTaskConfig().getGidScanPageSize();

        for (int shardIndex = 0; shardIndex < shardCount; shardIndex++) {
            String shardKey = "task:" + taskId + ":" + shardIndex;

            try {
                // 从Redis set中获取所有GID
                Set<String> gids = redisTemplate.opsForSet().members(shardKey);

                if (gids != null && !gids.isEmpty()) {
                    log.debug("Processing shard {} with {} gids for taskId={}", shardIndex, gids.size(), taskId);

                    // 分批处理GID
                    List<String> batch = new ArrayList<>(batchSize);
                    for (String gid : gids) {
                        batch.add(gid);

                        if (batch.size() == batchSize) {
                            consumer.accept(new ArrayList<>(batch));
                            batch.clear();
                        }
                    }

                    // 处理最后一个不足batchSize的批次
                    if (!batch.isEmpty()) {
                        consumer.accept(new ArrayList<>(batch));
                    }
                }

                // 处理完分片后删除Redis key
                redisTemplate.delete(shardKey);
                log.debug("Deleted shard key {} for taskId={}", shardKey, taskId);

            } catch (Exception e) {
                log.error("Error processing shard {} for taskId={}", shardIndex, taskId, e);
                throw new RuntimeException("Error processing shard " + shardIndex, e);
            }
        }
    }

}