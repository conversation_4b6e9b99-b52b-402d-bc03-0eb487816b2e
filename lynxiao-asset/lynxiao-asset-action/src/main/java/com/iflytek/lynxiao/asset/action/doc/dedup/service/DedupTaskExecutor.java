package com.iflytek.lynxiao.asset.action.doc.dedup.service;

import cn.hutool.core.thread.BlockPolicy;
import com.iflytek.lynxiao.asset.action.doc.dedup.config.DocDedupProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.CacheMongoTemplate;
import com.iflytek.lynxiao.data.constant.DedupType;
import com.iflytek.lynxiao.data.dto.asset.BucketDedupMessage;
import com.iflytek.lynxiao.data.message.dedup.DedupBucketMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.data.mongodb.core.query.Query;
import skynet.boot.pandora.support.MqSessionContext;
import skynet.boot.pandora.support.TaskCancelCache;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 去重任务执行器
 * 职责：执行去重任务的核心业务逻辑，包括GID筛选和去重计算
 *
 * <AUTHOR> by architect
 */
@Slf4j
public class DedupTaskExecutor implements AutoCloseable {

    private final DedupDocProcessor dedupProcessor;
    private final GidCache gidCache;
    private final DocDedupProperties dedupProperties;
    private final TaskCancelCache taskCancelCache;
    private final MqMessageCtrlSender mqMessageCtrlSender;
    private final BucketCacheService bucketCacheService;
    private final GidSelectionService gidSelectionService;
    private final ExecutorService dedupExecutor;

    public DedupTaskExecutor(DedupDocProcessor dedupProcessor,
                             GidCache gidCache,
                             DocDedupProperties dedupProperties,
                             TaskCancelCache taskCancelCache,
                             MqMessageCtrlSender mqMessageCtrlSender,
                             BucketCacheService bucketCacheService,
                             GidSelectionService gidSelectionService) {
        this.dedupProcessor = dedupProcessor;
        this.gidCache = gidCache;
        this.dedupProperties = dedupProperties;
        this.taskCancelCache = taskCancelCache;
        this.mqMessageCtrlSender = mqMessageCtrlSender;
        this.bucketCacheService = bucketCacheService;
        this.gidSelectionService = gidSelectionService;

        // 创建去重计算线程池
        this.dedupExecutor = new ThreadPoolExecutor(
                this.dedupProperties.getDocConfig().getDedupPoolSize(),
                this.dedupProperties.getDocConfig().getDedupPoolSize(),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(this.dedupProperties.getDocConfig().getDedupQueueSize()),
                new BasicThreadFactory.Builder().namingPattern("dedup-compute-%d").build(),
                new BlockPolicy()
        );
    }

    /**
     * 执行去重任务
     */
    public void execute(String taskId, BucketDedupMessage data, MqSessionContext sessionContext) {
        long startTime = System.currentTimeMillis();
        log.info("Starting dedup task execution. taskId={}, bucketCode={}", taskId, data.getBucketCode());

        try {
            // 异步发送筛选总数
            asyncSendTotalCount(taskId, data.getBucketCode());

            // 第一步：筛选重复GID
            long gidTotal = selectDuplicateGids(taskId, data);

            // 进度校正
            asyncSendTotalCount(taskId, data.getBucketCode());

            // 发送去重总数
            sendTotalCount(DedupType.STEP_TWO, taskId, gidTotal);

            // 第二步：计算去重结果
            if (gidTotal > 0) {
                computeDedupResults(taskId, data);
            } else {
                log.info("No duplicate GIDs found, skipping dedup computation. taskId={}, bucketCode={}",
                        taskId, data.getBucketCode());
                // 发送完成消息，表示没有需要去重的数据
                sendTotalCount(DedupType.STEP_TWO, taskId, 0);
            }

            log.info("Dedup task completed successfully. taskId={}, gidTotal={}, cost={}ms",
                    taskId, gidTotal, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("Dedup task execution failed. taskId={}", taskId, e);
            throw new RuntimeException("Dedup task execution failed", e);
        }
    }

    /**
     * 筛选重复的GID
     */
    private long selectDuplicateGids(String taskId, BucketDedupMessage data) throws Exception {
        try {
            log.info("Starting GID selection. taskId={}, bucketCode={}", taskId, data.getBucketCode());
            return gidSelectionService.selectDuplicateGids(taskId, data.getBucketCode());
        } catch (Exception e) {
            log.error("GID selection failed. taskId={}", taskId, e);
            sendFailureMessage(DedupType.STEP_ONE, taskId, e.getMessage());
            throw e;
        }
    }

    /**
     * 发送总数统计
     */
    private void sendTotalCount(String step, String taskId, long gidTotal) {
        try {
            mqMessageCtrlSender.sendTotal(step, taskId, gidTotal);
        } catch (Exception e) {
            log.error("Failed to send total count. taskId={}, gidTotal={}", taskId, gidTotal, e);
            throw new RuntimeException("Failed to send total count", e);
        }
    }

    /**
     * 异步发送总数统计
     * 使用虚拟线程异步执行数据库查询和消息发送，避免阻塞主线程
     */
    private void asyncSendTotalCount(String taskId, String bucketCode) {
        Thread.startVirtualThread(() -> {
            try {
                log.info("Starting async total count query. taskId={}, bucketCode={}", taskId, bucketCode);

                // 执行数据库查询
                CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
                long count = template.count(new Query(), bucketCode);

                log.info("Async total count completed. taskId={}, bucketCode={}, count={}",
                        taskId, bucketCode, count);

                // 发送总数消息
                mqMessageCtrlSender.sendTotal(DedupType.STEP_ONE, taskId, count);

            } catch (Exception e) {
                log.error("Failed to async send total count. taskId={}, bucketCode={}",
                        taskId, bucketCode, e);
                // 异步操作失败不抛出异常，避免影响主流程
            }
        });
    }

    /**
     * 计算去重结果
     */
    private void computeDedupResults(String taskId, BucketDedupMessage data) {
        long startTime = System.currentTimeMillis();
        AtomicLong computeTotal = new AtomicLong(0);
        AtomicInteger remainingTasks = new AtomicInteger(0);

        try {
            // 获取分片数量（重新计算）
            int shardCount = gidSelectionService.getShardCount(data.getBucketCode());

            // 使用分片扫描，直接for循环分片处理
            gidCache.scanShards(taskId, shardCount, gidBatch -> {
                // 已取消，不再提交线程
                if (taskCancelCache.isCancel(taskId)) {
                    log.info("dedup Task is cancel. taskId={}", taskId);
                    return;
                }
                computeTotal.getAndAdd(gidBatch.size());
                remainingTasks.incrementAndGet();
                dedupExecutor.submit(() -> {
                    try {
                        // 已取消，线程队列中的任务不再处理
                        if (taskCancelCache.isCancel(taskId)) {
                            log.info("dedup Task is cancel. taskId={}", taskId);
                            return;
                        }
                        DedupDocProcessor.DedupResult result = processGidBatch(taskId, data, gidBatch);
                        sendProcessingResult(taskId, result);
                    } catch (Exception e) {
                        log.error("Failed to process GID batch. taskId={}, batchSize={}",
                                taskId, gidBatch.size(), e);
                    } finally {
                        remainingTasks.decrementAndGet();
                    }
                });
            });

            // 等待所有计算任务完成
            waitForCompletion(taskId, remainingTasks);

            // 重置精确计数器
            sendTotalCount(DedupType.STEP_TWO, taskId, computeTotal.get());

            log.info("Dedup computation completed. taskId={}, totalProcessed={}, shardCount={}, cost={}ms",
                    taskId, computeTotal.get(), shardCount, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("Dedup computation failed. taskId={}", taskId, e);
            sendFailureMessage(DedupType.STEP_TWO, taskId, e.getMessage());
            throw e;
        }
    }

    /**
     * 处理单个GID批次
     */
    private DedupDocProcessor.DedupResult processGidBatch(String taskId, BucketDedupMessage data, List<String> gidBatch) {
        log.debug("Processing GID batch. taskId={}, batchSize={}, bucketCode={}", 
                taskId, gidBatch.size(), data.getBucketCode());
                
        DedupBucketMessage payload = new DedupBucketMessage();
        payload.setTaskId(taskId);
        payload.setGidList(gidBatch);
        payload.setBucketCode(data.getBucketCode());
        payload.setScript(data.getScript());
        payload.setScriptParam(data.getScriptParam());
        
        return dedupProcessor.run(payload);
    }

    /**
     * 发送处理结果
     */
    private void sendProcessingResult(String taskId, DedupDocProcessor.DedupResult result) {
        try {
            mqMessageCtrlSender.sendDone(DedupType.STEP_TWO, taskId, result.getDone());
            if (result.getFailed() > 0) {
                mqMessageCtrlSender.sendFail(DedupType.STEP_TWO, taskId, result.getFailed());
            }
        } catch (Exception e) {
            log.error("Failed to send processing result. taskId={}", taskId, e);
            throw new RuntimeException("Failed to send processing result", e);
        }
    }

    /**
     * 等待所有任务完成
     */
    private void waitForCompletion(String taskId, AtomicInteger remainingTasks) {
        long startWait = System.currentTimeMillis();
        long timeoutMillis = dedupProperties.getDocConfig().getTimeout().toMillis();
        
        while (remainingTasks.get() > 0) {
            if (System.currentTimeMillis() - startWait > timeoutMillis) {
                log.warn("Dedup computation timeout. taskId={}, remainingTasks={}", 
                        taskId, remainingTasks.get());
            }
            
            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Wait interrupted. taskId={}", taskId);
                break;
            }
        }
    }

    /**
     * 发送失败消息
     */
    private void sendFailureMessage(String stepType, String taskId, String errorMessage) {
        try {
            mqMessageCtrlSender.sendFailMsg(stepType, taskId, 0, errorMessage);
        } catch (Exception e) {
            log.error("Failed to send failure message. taskId={}, stepType={}", taskId, stepType, e);
        }
    }

    @Override
    public void close() throws Exception {
        log.info("Shutting down DedupTaskExecutor");

        if (gidSelectionService != null) {
            gidSelectionService.close();
        }

        // 关闭去重计算线程池
        if (dedupExecutor != null && !dedupExecutor.isShutdown()) {
            dedupExecutor.shutdown();
            try {
                if (!dedupExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Dedup executor did not terminate gracefully, forcing shutdown");
                    dedupExecutor.shutdownNow();

                    if (!dedupExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.error("Dedup executor did not terminate after forced shutdown");
                    }
                }
            } catch (InterruptedException e) {
                log.warn("Interrupted while shutting down dedup executor");
                dedupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("DedupTaskExecutor shutdown completed");
    }
}
