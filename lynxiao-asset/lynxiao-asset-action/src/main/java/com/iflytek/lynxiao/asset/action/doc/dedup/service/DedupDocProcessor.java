package com.iflytek.lynxiao.asset.action.doc.dedup.service;

import com.iflytek.lynxiao.asset.action.doc.dedup.config.DocDedupProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.CacheMongoTemplate;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.domain.AssetOperationType;
import com.iflytek.lynxiao.data.message.dedup.DedupBucketMessage;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 去重文档处理器 - 优化版本
 * 职责：执行文档去重计算，优化数据库批量操作性能
 *
 * <AUTHOR> by architect
 */
@Slf4j
public class DedupDocProcessor {
    private final DedupScriptManager scriptManager;
    private final BucketCacheService bucketCacheService;
    private final DocDedupProperties dedupProperties;

    public DedupDocProcessor(DedupScriptManager scriptManager, BucketCacheService bucketCacheService, DocDedupProperties dedupProperties) {
        this.scriptManager = scriptManager;
        this.bucketCacheService = bucketCacheService;
        this.dedupProperties = dedupProperties;
    }


    /**
     * 去重计算 - 优化版本
     *
     * 优化点：
     * 1. 批量查询文档，减少数据库交互次数
     * 2. 使用并发安全的数据结构
     * 3. 优化批量更新操作
     * 4. 增加详细的性能监控日志
     * 5. 分批处理GID，避免内存溢出
     */
    public DedupResult run(DedupBucketMessage data) {
        long startTime = System.currentTimeMillis();
        DedupResult dedupResult = DedupResult.builder().done(0).failed(0).build();

        try {
            log.debug("Starting dedup processing. bucketCode={}, gidCount={}",
                    data.getBucketCode(), data.getGidList().size());

            // 使用并发安全的集合
            CacheMongoTemplate template = bucketCacheService.getClient(data.getBucketCode());
            List<Long> usefulDocs = new CopyOnWriteArrayList<>();
            List<Long> uselessDocs = new CopyOnWriteArrayList<>();

            // 分批处理GID列表
            int batchSize = dedupProperties.getTaskConfig().getDedupBatchQuerySize();
            List<String> gidList = data.getGidList();

            for (int i = 0; i < gidList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, gidList.size());
                List<String> gidBatch = gidList.subList(i, endIndex);

                try {
                    // 批量查询当前批次的所有GID对应的文档
                    Map<String, List<Document>> gidDocMap = findDocsByGidBatch(
                            template.getMongoTemplate(), gidBatch, data.getBucketCode());

                    // 处理当前批次的每个GID
                    for (String gid : gidBatch) {
                        try {
                            List<Document> docs = gidDocMap.getOrDefault(gid, Collections.emptyList());
                            calculateSingleGid(data, gid, docs, usefulDocs, uselessDocs);
                        } catch (Exception e) {
                            log.error("Failed to process GID: {}", gid, e);
                            dedupResult.setFailed(dedupResult.getFailed() + 1);
                        }
                    }

                    log.debug("Processed GID batch. batchSize={}, startIndex={}, endIndex={}",
                            gidBatch.size(), i, endIndex);

                } catch (Exception e) {
                    log.error("Failed to process GID batch. startIndex={}, endIndex={}", i, endIndex, e);
                    dedupResult.setFailed(dedupResult.getFailed() + gidBatch.size());
                }
            }

            log.debug("Dedup calculation completed. usefulDocs={}, uselessDocs={}",
                    usefulDocs.size(), uselessDocs.size());

            // 批量更新文档标志
            updateDocFlags(data, usefulDocs, uselessDocs);

            dedupResult.setDone(data.getGidList().size() - dedupResult.getFailed());

            long cost = System.currentTimeMillis() - startTime;
            log.info("Dedup processing completed. bucketCode={}, gidCount={}, batchSize={}, cost={}ms",
                    data.getBucketCode(), data.getGidList().size(), batchSize, cost);

        } catch (Exception e) {
            log.error("Dedup processing failed. bucketCode={}", data.getBucketCode(), e);
            dedupResult.setFailed(data.getGidList().size());
        }

        return dedupResult;
    }

    /**
     * 批量查询多个GID的文档 - 优化数据库访问
     */
    private Map<String, List<Document>> findDocsByGidBatch(MongoTemplate mongoTemplate, List<String> gids, String bucketCode) {
        long startTime = System.currentTimeMillis();

        // 构建批量查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("gid").in(gids)
                .and(AssetCellProps.KEY_OP).ne(AssetOperationType.DELETE.getValue())
                .and(AssetCellProps.KEY_A).ne(AssetAuditStatus.REJECT.getValue())
        );

        List<Document> allDocuments = mongoTemplate.find(query, Document.class, bucketCode);

        // 按GID分组
        Map<String, List<Document>> gidDocMap = allDocuments.stream()
                .collect(Collectors.groupingBy(doc -> doc.getString("gid")));

        long cost = System.currentTimeMillis() - startTime;
        log.debug("Batch find documents completed. bucketCode={}, gidCount={}, totalDocCount={}, cost={}ms",
                bucketCode, gids.size(), allDocuments.size(), cost);

        return gidDocMap;
    }

    /**
     * 单个GID查询文档 - 保留作为备用方法
     */
    private List<Document> findDocsByGid(MongoTemplate mongoTemplate, String gid, String bucketCode) {
        long startTime = System.currentTimeMillis();

        Query query = new Query();
        query.addCriteria(Criteria.where("gid").is(gid)
                .and(AssetCellProps.KEY_OP).ne(AssetOperationType.DELETE.getValue())
                .and(AssetCellProps.KEY_A).ne(AssetAuditStatus.REJECT.getValue())
        );

        List<Document> allDocuments = mongoTemplate.find(query, Document.class, bucketCode);

        long cost = System.currentTimeMillis() - startTime;
        log.debug("Single find documents completed. bucketCode={}, gid={}, docCount={}, cost={}ms",
                bucketCode, gid, allDocuments.size(), cost);

        return allDocuments;
    }

    /**
     * 计算单个GID的去重结果
     */
    private void calculateSingleGid(DedupBucketMessage data, String gid, List<Document> originDocList,
                                   List<Long> usefulDocs, List<Long> uselessDocs) {
        log.trace("Processing GID: {}", gid);

        if (CollectionUtils.isEmpty(originDocList)) {
            log.debug("GID {} does not need deduplication. docCount={}", gid,
                    originDocList != null ? originDocList.size() : 0);
            return;
        }

        if (originDocList.size() == 1) {
            usefulDocs.add(originDocList.getFirst().getLong("_id"));
            return;
        }

        long scriptStartTime = System.currentTimeMillis();
        List<Document> usefulDocList;

        try {
            // 执行去重脚本
            usefulDocList = scriptManager.getExecutor().execute(
                    data.getScript(), originDocList, data.getScriptParam());

            long scriptCost = System.currentTimeMillis() - scriptStartTime;
            log.debug("Script execution completed. gid={}, cost={}ms", gid, scriptCost);

        } catch (Exception e) {
            log.error("Script execution failed. gid={}, script={}, originDocCount={}",
                    gid, data.getScript(), originDocList.size(), e);
            throw new RuntimeException("Script execution failed for GID: " + gid, e);
        }

        if (CollectionUtils.isEmpty(usefulDocList)) {
            log.error("Script returned empty result. gid={}, script={}, originDocCount={}",
                    gid, data.getScript(), originDocList.size());
            throw new RuntimeException("Script returned empty result for GID: " + gid);
        }

        log.trace("Dedup result. gid={}, originalCount={}, usefulCount={}",
                gid, originDocList.size(), usefulDocList.size());

        // 提取文档ID
        List<Long> allIds = originDocList.stream()
                .map(doc -> doc.getLong("_id"))
                .toList();

        List<Long> usefulIds = usefulDocList.stream()
                .map(doc -> doc.getLong("_id"))
                .toList();

        // 计算无用的文档ID
        List<Long> uselessIds = new ArrayList<>(allIds);
        uselessIds.removeAll(usefulIds);

        usefulDocs.addAll(usefulIds);
        uselessDocs.addAll(uselessIds);
    }

    /**
     * 批量更新文档标志 - 优化版本
     */
    private void updateDocFlags(DedupBucketMessage data, List<Long> usefulDocs, List<Long> uselessDocs) {
        if (usefulDocs.isEmpty() && uselessDocs.isEmpty()) {
            log.debug("No documents to update flags. bucketCode={}", data.getBucketCode());
            return;
        }

        long startTime = System.currentTimeMillis();
        CacheMongoTemplate template = bucketCacheService.getClient(data.getBucketCode());
        long now = System.currentTimeMillis();

        try {
            // 使用UNORDERED模式提升批量操作性能
            BulkOperations bulkOps = template.getMongoTemplate()
                    .bulkOps(BulkOperations.BulkMode.UNORDERED, data.getBucketCode());

            // 批量设置有用文档标志
            if (!usefulDocs.isEmpty()) {
                Query usefulQuery = new Query(Criteria.where("_id").in(usefulDocs));
                Update usefulUpdate = new Update()
                        .set(AssetCellProps.KEY_S, AssetCellProps.BoolValue.FALSE.getValue())
                        .set(AssetCellProps.KEY_UPDATE_TS, now);
                bulkOps.updateMulti(usefulQuery, usefulUpdate);

                log.debug("Added useful docs update operation. count={}", usefulDocs.size());
            }

            // 批量设置无用文档标志
            if (!uselessDocs.isEmpty()) {
                Query uselessQuery = new Query(Criteria.where("_id").in(uselessDocs));
                Update uselessUpdate = new Update()
                        .set(AssetCellProps.KEY_S, AssetCellProps.BoolValue.TRUE.getValue())
                        .set(AssetCellProps.KEY_UPDATE_TS, now);
                bulkOps.updateMulti(uselessQuery, uselessUpdate);

                log.debug("Added useless docs update operation. count={}", uselessDocs.size());
            }

            // 执行批量操作
            var result = bulkOps.execute();

            long cost = System.currentTimeMillis() - startTime;
            log.info("Bulk update completed. bucketCode={}, usefulCount={}, uselessCount={}, " +
                    "modifiedCount={}, cost={}ms",
                    data.getBucketCode(), usefulDocs.size(), uselessDocs.size(),
                    result.getModifiedCount(), cost);

        } catch (Exception e) {
            log.error("Failed to update document flags. bucketCode={}, usefulCount={}, uselessCount={}",
                    data.getBucketCode(), usefulDocs.size(), uselessDocs.size(), e);
            throw new RuntimeException("Failed to update document flags", e);
        }
    }

    /**
     * 根据GID查找文档 - 保留用于兼容性，建议使用批量查询
     * @deprecated 建议使用 batchFindDocumentsByGids 进行批量查询以提升性能
     */
    @Deprecated
    private List<Document> findDocByGid(DedupBucketMessage data, String gid) {
        CacheMongoTemplate template = bucketCacheService.getClient(data.getBucketCode());
        log.debug("Finding documents by GID. collection={}, gid={}", data.getBucketCode(), gid);

        Query query = new Query();
        query.addCriteria(Criteria.where("gid").is(gid));

        return template.getMongoTemplate().find(query, Document.class, data.getBucketCode());
    }



    @Getter
    @Setter
    @Builder
    public static class DedupResult {
        private long done;

        private long failed;
    }
}
