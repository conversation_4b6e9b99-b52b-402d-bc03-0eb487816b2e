package com.iflytek.lynxiao.asset.action.doc.dedup.config;

import com.iflytek.lynxiao.asset.action.doc.dedup.DocDedupHandler;
import com.iflytek.lynxiao.asset.action.doc.dedup.condition.ConditionalOnDocDedup;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.DedupDocProcessor;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.DedupScriptExecutor4Groovy;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.DedupScriptExecutor4Mock;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.DedupScriptManager;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.DedupTaskExecutor;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.GidCache;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.GidSelectionService;
import com.iflytek.lynxiao.asset.action.doc.dedup.service.MqMessageCtrlSender;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import skynet.boot.pandora.annotation.SkynetPandoraMqHandler;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.support.TaskCancelCache;

/**
 * 去重配置 - 优化版本
 * 统一管理所有去重相关的Bean配置
 */
@Configuration(proxyBeanMethods = false)
@ImportAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@ConditionalOnDocDedup
public class DocDedupConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.stream.dedup")
    public DocDedupProperties dedupProperties() {
        return new DocDedupProperties();
    }


    @Bean
    public DedupScriptManager dedupScriptManager(DocDedupProperties properties, ApplicationContext applicationContext) {
        return new DedupScriptManager(properties, applicationContext);
    }

    @Bean
    public GidCache gidCache(DocDedupProperties properties, @Qualifier("stringRedisTemplate") RedisTemplate<String, String> redisTemplate) {
        return new GidCache(properties, redisTemplate);
    }


    @Bean("DedupScriptExecutor4Mock")
    public DedupScriptExecutor4Mock dedupScriptExecutor4Mock() {
        return new DedupScriptExecutor4Mock();
    }


    @Bean("DedupScriptExecutor4Groovy")
    public DedupScriptExecutor4Groovy dedupScriptExecutor4Groovy() {
        return new DedupScriptExecutor4Groovy();
    }


    @Bean
    public DedupDocProcessor dedupDocMqHandler(DedupScriptManager scriptManager, BucketCacheService bucketCacheService, DocDedupProperties docDedupProperties) {
        return new DedupDocProcessor(scriptManager, bucketCacheService, docDedupProperties);
    }

    @Bean
    public DedupTaskExecutor dedupTaskExecutor(DedupDocProcessor dedupDocProcessor,
                                               GidCache gidCache,
                                               DocDedupProperties docDedupProperties,
                                               TaskCancelCache taskCancelCache,
                                               MqMessageCtrlSender mqMessageCtrlSender,
                                               BucketCacheService bucketCacheService,
                                               GidSelectionService gidSelectionService) {
        return new DedupTaskExecutor(dedupDocProcessor, gidCache, docDedupProperties,
                                   taskCancelCache, mqMessageCtrlSender, bucketCacheService, gidSelectionService);
    }

    @Bean
    public GidSelectionService gidSelectionService(DocDedupProperties docDedupProperties,
                                                   BucketCacheService bucketCacheService,
                                                   TaskCancelCache taskCancelCache,
                                                   MqMessageCtrlSender mqMessageCtrlSender,
                                                   GidCache gidCache
                                                   ) {
        return new GidSelectionService(
                docDedupProperties, bucketCacheService, taskCancelCache, mqMessageCtrlSender, gidCache
        );
    }

    @Bean
    @SkynetPandoraMqHandler
    public DocDedupHandler docDedupHandler(DedupTaskExecutor dedupTaskExecutor,
                                           MqMessageCtrlSender mqMessageCtrlSender) {
        return new DocDedupHandler(dedupTaskExecutor, mqMessageCtrlSender);
    }

    @Bean
    public MqMessageCtrlSender mqMessageCtrlSender(DocDedupProperties docDedupProperties,
                                                   PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder) {
        return new MqMessageCtrlSender(docDedupProperties, pandoraApiRequestObserverBuilder);
    }


    @Bean
    public RedisTemplate<String, String> stringRedisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer()); // 设置 value 序列化器
        return template;
    }
}
