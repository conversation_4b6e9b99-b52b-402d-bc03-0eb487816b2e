#------------------------------------------------------------------------------------------------------
# Lynxiao Feature Configuration
#------------------------------------------------------------------------------------------------------
lynxiao.asset.action.doc.dedup.enabled=true
lynxiao.mongo.enabled=false
#------------------------------------------------------------------------------------------------------
# Endpoint Configuration
skynet.pandora.mq.endpoint.dedup.enabled=true
skynet.pandora.mq.endpoint.dedup.handler-size=32
skynet.pandora.mq.endpoint.dedup.handle-timeout=30m
# Consumer Topics Configuration
skynet.pandora.mq.consumer-topics.lynxiao-asset-dedup.enabled=${lynxiao.asset.action.doc.dedup.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-dedup.consumer-size=4
skynet.pandora.mq.consumer-topics.lynxiao-asset-dedup.handle-endpoint=dedup
skynet.pandora.mq.consumer-topics.lynxiao-asset-dedup.group=${SKYNET_PANDORA_MQ_CONSUMER_GROUP}
#------------------------------------------------------------------------------------------------------
# Redis Configuration
#------------------------------------------------------------------------------------------------------
spring.data.redis.cluster.nodes=${turing.cloud.paas.redis.nodes}
spring.data.redis.password=${turing.cloud.paas.redis.password}
spring.data.redis.timeout=5s
spring.data.redis.lettuce.pool.max-active=256
spring.data.redis.lettuce.pool.max-idle=50
spring.data.redis.lettuce.pool.min-idle=10
spring.data.redis.lettuce.pool.max-wait=10s
spring.data.redis.lettuce.cluster.refresh.adaptive=true
spring.data.redis.lettuce.cluster.refresh.period=5s
spring.data.redis.lettuce.shutdown-timeout=100ms

#------------------------------------------------------------------------------------------------------
# Dedup Task Configuration
#------------------------------------------------------------------------------------------------------
# 每个分片存储的GID个数，用于计算分片数量
lynxiao.asset.dedup.task-config.gids-per-shard=10000
# 其他去重配置
lynxiao.asset.dedup.task-config.partition=20
lynxiao.asset.dedup.task-config.page-size=1000
lynxiao.asset.dedup.task-config.query-doc-timeout=2h
lynxiao.asset.dedup.task-config.pipeline-queue-size=10000
lynxiao.asset.dedup.task-config.pipeline-write-limit=1000
lynxiao.asset.dedup.task-config.pipeline-write-wait=1000ms
lynxiao.asset.dedup.task-config.expected-insertions=120000000
lynxiao.asset.dedup.task-config.false-positive-probability=0.001
lynxiao.asset.dedup.task-config.gid-ttl=2h
lynxiao.asset.dedup.task-config.gid-scan-page-size=500
lynxiao.asset.dedup.task-config.statistics-period=1m

# 去重文档处理配置
lynxiao.asset.dedup.doc-config.script-type=groovy
lynxiao.asset.dedup.doc-config.dedup-pool-size=20
lynxiao.asset.dedup.doc-config.dedup-queue-size=200
lynxiao.asset.dedup.doc-config.timeout=5m