package com.iflytek.lynxiao.asset.portal.controller;

import com.mongodb.client.result.UpdateResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.Binary;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.common.GZipUtils;
import skynet.boot.mongo.DocumentCompressor;
import skynet.boot.pandora.api.ApiResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2025/08/01
 */
@Tag(name = "test管理")
@RestController
@RequestMapping("/api/v1/test")
@EnableSkynetSwagger2
@Slf4j
public class TestController {

    private final DocumentCompressor documentCompressor = new DocumentCompressor();

    @Operation(summary = "test")
    @GetMapping("/update")
    public ApiResponse update() {

        // 示例用法
        String filePath = "/Users/<USER>/Downloads/data-process/120_result1.txt";

        MongoTemplate mongoTemplate =
                createMongoTemplate("************************************************************************************************************************************");
        MongoTemplate shardTemplate =
                createMongoTemplate("mongodb+srv://u_lynxiao:<EMAIL>/lynxiao_datashard?authSource=admin&tls=false&ssl=false");
        // 统计信息
        int totalCount = 0;
        int successCount = 0;
        int errorCount = 0;

        Path path = Paths.get(filePath);

        try (BufferedReader reader = Files.newBufferedReader(path)) {
            String line;

            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                try {
                    Long id = Long.parseLong(line);
                    totalCount++;

                    // 逐个更新
                    boolean success = updateSingleRecord(id, mongoTemplate, shardTemplate);
                    if (success) {
                        successCount++;
                    } else {
                        errorCount++;
                    }

                    // 每处理100条记录输出一次进度
                    if (totalCount % 100 == 0) {
                        log.info("处理进度: {}, 成功: {}, 失败: {}", totalCount, successCount, errorCount);
                    }

                } catch (NumberFormatException e) {
                    log.warn("无法解析ID: {}", line);
                    errorCount++;
                }
            }

        } catch (IOException e) {
            log.error("读取文件失败: {}", filePath, e);
            throw new RuntimeException("读取文件失败", e);
        }

        log.info("处理完成 - 总数: {}, 成功: {}, 失败: {}", totalCount, successCount, errorCount);

        return new ApiResponse();
    }

    private boolean updateSingleRecord(Long id, MongoTemplate mongoTemplate, MongoTemplate shardTemplate) {
        Query query = Query.query(Criteria.where("_id").is(id));
        query.fields().include("path");
        Document one = mongoTemplate.findOne(query, Document.class, "site_zysj_com_cn");
        String path = one.getString("path");
        path = path.replace("${", "").replace("}", "");
        String[] split = path.split(":");
        String collectionName = split[0];
        String md5Id = split[1];
        Document content = shardTemplate.findOne(Query.query(Criteria.where("_id").is(md5Id)), Document.class, collectionName);
        String s = new String(GZipUtils.decompress(((Binary)content.get("d")).getData()));

        mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(id)), Update.update("path", s), "site_zysj_com_cn");

        return true;
    }


    /**
     * 创建MongoTemplate
     */
    private MongoTemplate createMongoTemplate(String mongoUri) {
        return new MongoTemplate(new SimpleMongoClientDatabaseFactory(mongoUri));
    }

    /**
     * 更新单条记录
     */
    private boolean updateSingleRecord(MongoTemplate mongoTemplate,
                                       String collectionName,
                                       Long id,
                                       String updateField,
                                       Object updateValue) {
        try {
            // 构建查询条件
            Query query = new Query(Criteria.where("_id").is(id));

            // 构建更新操作
            Update update = new Update()
                    .set(updateField, updateValue)
                    .set("updateTime", Instant.now()); // 添加更新时间戳

            // 执行更新
            UpdateResult result = mongoTemplate.updateFirst(query, update, collectionName);

            if (result.getModifiedCount() > 0) {
                log.debug("成功更新ID: {}", id);
                return true;
            } else {
                log.warn("未找到ID: {}", id);
                return false;
            }

        } catch (Exception e) {
            log.error("更新失败 - ID: {}, 错误: {}", id, e.getMessage());
            return false;
        }
    }

}
