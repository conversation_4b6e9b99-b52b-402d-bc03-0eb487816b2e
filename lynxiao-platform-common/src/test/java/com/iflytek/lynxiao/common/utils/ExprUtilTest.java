package com.iflytek.lynxiao.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.micrometer.core.instrument.util.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.Objects;

/**
 * @author: leitong
 * @date: 2024/10/16 19:30
 * @description:
 **/
public class ExprUtilTest {

    @Test
    public void testCase1() throws IOException {
        String s = loadContentFromClasspath("case1.json");
        Map<String, Object> query = ExprUtil.toMongoQuery(s);
        Assert.assertTrue(!CollectionUtils.isEmpty(query));
        System.out.println(JSON.toJSONString(query));

    }

    @Test
    public void testCase2() throws IOException {
        String s = loadContentFromClasspath("case2.json");
        Map<String, Object> query = ExprUtil.toMongoQuery(s);
        Assert.assertTrue(!CollectionUtils.isEmpty(query));
        System.out.println(JSON.toJSONString(query));

    }

    @Test
    public void testCase3() throws IOException {
        String s = loadContentFromClasspath("case3.json");
        Map<String, Object> query = ExprUtil.toMongoQuery(s);
        Assert.assertTrue(!CollectionUtils.isEmpty(query));
        System.out.println(JSON.toJSONString(query));
    }

    @Test
    public void testCase4() throws IOException {
        String s = loadContentFromClasspath("case4.json");
        Map<String, Object> query = ExprUtil.toMongoQuery(s);
        Assert.assertTrue(!CollectionUtils.isEmpty(query));
        System.out.println(JSON.toJSONString(query));
    }

    @Test
    public void testCase5() throws IOException {
        String s = loadContentFromClasspath("case5.json");
        IllegalArgumentException e = null;
        try{
            Map<String, Object> query = ExprUtil.toMongoQuery(s);
            System.out.println(JSON.toJSONString(query));
        }catch (IllegalArgumentException ie){
            e = ie;
        }
        Assert.assertTrue(Objects.nonNull(e));
        Assert.assertTrue(StringUtils.isNotBlank(e.getMessage()));
    }

    @Test
    public void testIsConditionMet() throws IOException {
        String s = loadContentFromClasspath("case1.json");
        JSONObject map = JSON.parseObject(s);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("summary", "rty");
        jsonObject.put("extras-teshu", "asd");
        jsonObject.put("title", "bcddfasdfasdfasdfadsdafsdf");
        jsonObject.put("url", "qwe");
        jsonObject.put("site", "20");
        boolean result = ExprUtil.isConditionMet(map, jsonObject);
        System.out.println(result);
    }

    @Test
    public void testIsConditionMet2() {
        String s = "{\"nodes\":[{\"field\":\"content\",\"args\":[\"123\",\"456\"],\"op\":\"START_WITH\"," +
                "\"@type\":\"STRING\"}],\"@type\":\"AND\"}";
        JSONObject condition = JSONObject.parseObject(s);
        JSONObject jsonObject = new JSONObject();
        // jsonObject.put("len", 300);
        jsonObject.put("content", "543");
        boolean result = ExprUtil.isConditionMet(condition, jsonObject);
        System.out.println(result);
    }

    @Test
    public void testIsConditionMet3() {
        String s = """
                {"nodes":[{"field":"len","op":"BETWEEN","@type":"INT","args":[100,1000]},{"field":"post_ts","args":["1546272000"],"op":"GE","@type":"LONG"},{"@type":"OR","nodes":[{"@type":"AND","nodes":[{"field":"hospital_level","args":["三级","三甲"],"op":"CONTAINS_ANY","@type":"STRING"},{"field":"doctor_level","args":["主任医师","副主任医师"],"op":"NOT_CONTAINS_ANY","@type":"STRING"}]},{"@type":"AND","nodes":[{"field":"doctor_level","args":["主任医师","副主任医师"],"op":"CONTAINS_ANY","@type":"STRING"},{"field":"hospital_level","args":["三级","三甲"],"op":"NOT_CONTAINS_ANY","@type":"STRING"}]}]},{"field":"domain","args":["www.youlai.cn","youlai.cn","m.youlai.cn"],"op":"IN","@type":"STRING"},{"@type":"OR","nodes":[{"field":"path","args":["^/yyk/article.*"],"op":"REGEX","@type":"STRING"},{"field":"path","args":["^/video/article/.*"],"op":"REGEX","@type":"STRING"},{"field":"path","args":["^/ask/.*"],"op":"REGEX","@type":"STRING"}]},{"@type":"OR","nodes":[{"field":"title","args":["${question_desc}"],"op":"EQ","@type":"STRING"},{"field":"question_desc","args":["","null"],"op":"IN","@type":"STRING"}]}],"@type":"AND"}
                """;
        JSONObject condition = JSONObject.parseObject(s);
        String data = """
                {"_id":673974282597335600,"_x":{"uts":1754038433859,"cts":1754030234891,"cat":1754030234891,"df":0,"v":0,"au":"解明敏","at":1754038433857,"a":1,"op":2,"sbc":"YLSSINOC_WEB_V0KML0UU"},"content":"${ldoc_003:a4d658e62b2b5c5938bdfbef836e0202}","len":160,"oplog":"update","post_ts":1681401600,"readable_content":"${ldoc_003:a4d658e62b2b5c5938bdfbef836e0202}","title":"茅莓的营养价值","hospital_level":"三甲","hospital":"山西省中医院","doctor_name":"倪文杰","doctor_avatar":"https://file.youlai.cn/cnkfile1/M01/2C/2E/o4YBAFta4CaAc4XMAACLbSlFS0o61.jpeg","depart":"","question_desc":"茅莓的营养价值","doctor_level":"主治医师","domain":"www.youlai.cn","protocol":"https","path":"/ask/1318606.html","site":"youlai.cn","gid":"bfa04d7f8f757789bc8a24e264bfa219a1e4ee29e4728a04a10e6924c7942766","q_score":0.9986711740493774}
                """;
        JSONObject jsonObject = JSONObject.parseObject(data);
        boolean result = ExprUtil.isConditionMet(condition, jsonObject);
        System.out.println(result);
    }

    @Test
    public void testIsConditionMetWithNestedFields() {
        // 测试嵌套字段 a.b 的情况
        String conditionJson = "{\"nodes\":[{\"field\":\"a.b\",\"args\":[\"test\"],\"op\":\"EQ\"," +
                "\"@type\":\"STRING\"}],\"@type\":\"AND\"}";
        JSONObject condition = JSONObject.parseObject(conditionJson);

        // 创建包含嵌套字段的测试数据
        JSONObject nestedObject = new JSONObject();
        nestedObject.put("b", "test");

        JSONObject testData = new JSONObject();
        testData.put("a", nestedObject);

        // 测试嵌套字段匹配
        boolean result = ExprUtil.isConditionMet(condition, testData);
        Assert.assertTrue("嵌套字段 a.b 应该匹配成功", result);

        // 测试嵌套字段不匹配的情况
        nestedObject.put("b", "different");
        boolean result2 = ExprUtil.isConditionMet(condition, testData);
        Assert.assertFalse("嵌套字段 a.b 应该匹配失败", result2);

        // 测试字段不存在的情况
        JSONObject testData2 = new JSONObject();
        testData2.put("a", new JSONObject()); // a存在但a.b不存在
        boolean result3 = ExprUtil.isConditionMet(condition, testData2);
        Assert.assertFalse("不存在的嵌套字段应该匹配失败", result3);

        System.out.println("嵌套字段测试通过");
    }

    @Test
    public void testIsConditionMetWithDeepNestedFields() {
        // 测试更深层的嵌套字段 a.b.c 的情况
        String conditionJson = "{\"nodes\":[{\"field\":\"a.b.c\",\"args\":[\"deep\"],\"op\":\"EQ\"," +
                "\"@type\":\"STRING\"}],\"@type\":\"AND\"}";
        JSONObject condition = JSONObject.parseObject(conditionJson);

        // 创建包含深层嵌套字段的测试数据
        JSONObject deepObject = new JSONObject();
        deepObject.put("c", "deep");

        JSONObject middleObject = new JSONObject();
        middleObject.put("b", deepObject);

        JSONObject testData = new JSONObject();
        testData.put("a", middleObject);

        // 测试深层嵌套字段匹配
        boolean result = ExprUtil.isConditionMet(condition, testData);
        Assert.assertTrue("深层嵌套字段 a.b.c 应该匹配成功", result);

        System.out.println("深层嵌套字段测试通过");
    }

    @Test
    public void testIsConditionMetWithNestedFieldsNumber() {
        // 测试嵌套字段的数字类型
        String conditionJson = "{\"nodes\":[{\"field\":\"data.count\",\"args\":[\"100\"],\"op\":\"GT\"," +
                "\"@type\":\"LONG\"}],\"@type\":\"AND\"}";
        JSONObject condition = JSONObject.parseObject(conditionJson);

        // 创建包含嵌套数字字段的测试数据
        JSONObject dataObject = new JSONObject();
        dataObject.put("count", 150);

        JSONObject testData = new JSONObject();
        testData.put("data", dataObject);

        // 测试嵌套数字字段匹配
        boolean result = ExprUtil.isConditionMet(condition, testData);
        Assert.assertTrue("嵌套数字字段 data.count 应该匹配成功", result);

        System.out.println("嵌套数字字段测试通过");
    }

    @Test
    public void testIsConditionMetWithNullValues() {
        // 测试当嵌套字段值为null时的情况
        String conditionJson = "{\"nodes\":[{\"field\":\"a.b\",\"args\":[\"test\"],\"op\":\"EQ\"," +
                "\"@type\":\"STRING\"}],\"@type\":\"AND\"}";
        JSONObject condition = JSONObject.parseObject(conditionJson);

        // 创建包含null值的测试数据
        JSONObject nestedObject = new JSONObject();
        nestedObject.put("b", null); // 字段存在但值为null

        JSONObject testData = new JSONObject();
        testData.put("a", nestedObject);

        // 测试null值应该返回false
        boolean result = ExprUtil.isConditionMet(condition, testData);
        Assert.assertFalse("null值应该匹配失败", result);

        // 测试完全不存在的字段
        JSONObject testData2 = new JSONObject();
        testData2.put("x", "some value"); // 完全不相关的字段

        boolean result2 = ExprUtil.isConditionMet(condition, testData2);
        Assert.assertFalse("不存在的字段应该匹配失败", result2);

        System.out.println("null值处理测试通过");
    }

    private static String loadContentFromClasspath(String filename) throws IOException {
        String ret = null;
        try(InputStream in = ExprUtilTest.class.getClassLoader().getResourceAsStream("expr/" + filename)){
            if(in != null){
                ret = IOUtils.toString(in, Charset.forName("utf-8"));
            }
        }
        return ret;
    }
}