# 去重任务执行器优化总结

## 优化概述

本次优化主要针对 `DedupTaskExecutor` 的 `execute` 方法进行了三个关键优化点的改造，以提升性能和减少Redis操作开销。

## 优化点详细说明

### 1. selectDuplicateGids 优化

**优化前：**
- 直接调用 `gidSelectionService.selectDuplicateGids()` 方法
- 没有预先获取总数信息

**优化后：**
- 在 `GidSelectionService` 中添加了 `getTotalDocumentCount()` 方法
- 使用 MongoDB 的 `estimatedDocumentCount()` 获取预估值，比 `count()` 更快
- 如果预估失败，使用精确计数作为备选方案
- 根据总数计算分片数量

**相关代码文件：**
- `GidSelectionService.java` - 添加了总数获取和分片计算逻辑
- `DocDedupProperties.java` - 添加了 `gidsPerShard` 配置项

### 2. GidFilterPipelineHandler 改造

**优化前：**
- 每个GID直接存储为单独的Redis key：`task:${taskId}:gid:${gid}`
- 使用字符串类型存储

**优化后：**
- 根据GID的hash值路由到对应分片
- 使用Redis Set存储，key格式：`task:${taskId}:${分片索引}`
- 每个分片存储多个GID，减少Redis key数量
- 添加了分片数量参数到构造函数

**分片算法：**
```java
int shardIndex = Math.abs(gid.hashCode()) % shardCount;
```

**相关代码文件：**
- `GidFilterPipelineHandler.java` - 修改构造函数和存储逻辑
- `GidCache.java` - 添加 `addBatchWithSharding()` 方法

### 3. computeDedupResults 优化

**优化前：**
- 使用 `gidCache.scan()` 扫描所有Redis key
- 使用Redis SCAN命令遍历所有key

**优化后：**
- 使用 `gidCache.scanShards()` 直接for循环处理分片
- 每个分片处理完后立即删除Redis key
- 避免了SCAN操作的性能开销

**处理流程：**
1. 重新计算分片数量（基于bucketCode）
2. for循环遍历每个分片
3. 使用 `SMEMBERS` 获取分片中的所有GID
4. 分批处理GID
5. 处理完成后删除分片key

**相关代码文件：**
- `DedupTaskExecutor.java` - 修改计算逻辑，直接调用服务获取分片数量
- `GidCache.java` - 添加 `scanShards()` 方法

## 配置项说明

### 新增配置项

在 `application-doc-dedup.properties` 中添加：

```properties
# 每个分片存储的GID个数，用于计算分片数量
lynxiao.asset.dedup.task-config.gids-per-shard=10000
```

### 分片数量计算公式

```java
int shardCount = (int) Math.ceil((double) totalDocumentCount / gidsPerShard);
// 确保至少有1个分片，最多不超过1000个分片
shardCount = Math.max(1, Math.min(shardCount, 1000));
```

## 性能优化效果

### 1. MongoDB查询优化
- 使用 `estimatedDocumentCount()` 替代 `count()`，查询速度提升显著
- 预估值足够准确，满足分片计算需求

### 2. Redis存储优化
- 减少Redis key数量：从 N个GID对应N个key，优化为 分片数个key
- 使用Redis Set数据结构，批量操作更高效
- 示例：100万个GID，原来需要100万个key，现在只需要100个key（假设gidsPerShard=10000）

### 3. Redis扫描优化
- 避免使用SCAN命令遍历大量key
- 直接访问已知的分片key，性能更稳定
- 处理完分片后立即删除，减少内存占用

## 代码变更文件列表

1. **配置类**
   - `DocDedupProperties.java` - 添加分片配置项

2. **服务类**
   - `GidSelectionService.java` - 添加总数获取和分片计算
   - `GidFilterPipelineHandler.java` - 支持分片存储
   - `GidCache.java` - 添加分片存储和扫描方法
   - `DedupTaskExecutor.java` - 集成分片逻辑

3. **配置文件**
   - `application-doc-dedup.properties` - 添加新配置项

## 代码简化改进

### 移除不必要的全局变量

**优化前：**
- 使用 `taskShardCounts` Map 缓存任务的分片数量
- 需要在任务完成后手动清理缓存

**优化后：**
- 直接在需要时调用 `gidSelectionService.getShardCount()` 计算分片数量
- 无需额外的内存缓存，代码更简洁
- 避免了内存泄漏的风险

## 注意事项

1. **向后兼容性**：保留了原有的 `addBatch()` 和 `scan()` 方法，确保向后兼容

2. **错误处理**：添加了完善的异常处理和日志记录

3. **资源清理**：任务完成后自动清理Redis key，无需额外的内存管理

4. **配置灵活性**：分片大小可通过配置调整，适应不同规模的数据

5. **性能监控**：添加了详细的性能日志，便于监控和调优

6. **代码简洁性**：移除了不必要的全局状态，降低了代码复杂度

## 使用建议

1. **gidsPerShard配置**：建议根据实际数据量调整，一般设置为10000-50000
2. **监控分片数量**：关注日志中的分片数量，避免过度分片或分片不足
3. **Redis内存监控**：虽然优化了存储结构，仍需监控Redis内存使用情况
