# 去重批量查询优化总结

## 优化背景

### 原有问题
- **性能瓶颈**：每个GID都要单独查询一次数据库，导致大量的数据库交互
- **资源浪费**：频繁的数据库连接和查询操作，增加了数据库负载
- **处理效率低**：当GID数量较多时，整体处理时间过长

### 优化目标
- 减少数据库查询次数，提升处理性能
- 避免全量查询导致的内存溢出风险
- 保持代码的可维护性和可配置性

## 优化方案

### 1. 配置化批量大小

**新增配置项：**
```properties
# 去重处理时批量查询GID的大小，避免单个查询和全量查询的极端情况
lynxiao.asset.dedup.task-config.dedup-batch-query-size=100
```

**配置类更新：**
```java
/**
 * 去重处理时批量查询GID的大小
 * 避免单个GID查询和全量查询的极端情况
 */
private int dedupBatchQuerySize = 100;
```

### 2. 批量查询实现

**核心优化逻辑：**
```java
// 分批处理GID列表
int batchSize = dedupProperties.getTaskConfig().getDedupBatchQuerySize();
List<String> gidList = data.getGidList();

for (int i = 0; i < gidList.size(); i += batchSize) {
    int endIndex = Math.min(i + batchSize, gidList.size());
    List<String> gidBatch = gidList.subList(i, endIndex);
    
    // 批量查询当前批次的所有GID对应的文档
    Map<String, List<Document>> gidDocMap = findDocsByGidBatch(
            template.getMongoTemplate(), gidBatch, data.getBucketCode());
    
    // 处理当前批次的每个GID
    for (String gid : gidBatch) {
        List<Document> docs = gidDocMap.getOrDefault(gid, Collections.emptyList());
        calculateSingleGid(data, gid, docs, usefulDocs, uselessDocs);
    }
}
```

**批量查询方法：**
```java
private Map<String, List<Document>> findDocsByGidBatch(MongoTemplate mongoTemplate, List<String> gids, String bucketCode) {
    // 构建批量查询条件
    Query query = new Query();
    query.addCriteria(Criteria.where("gid").in(gids)
            .and(AssetCellProps.KEY_OP).ne(AssetOperationType.DELETE.getValue())
            .and(AssetCellProps.KEY_A).ne(AssetAuditStatus.REJECT.getValue())
    );

    List<Document> allDocuments = mongoTemplate.find(query, Document.class, bucketCode);

    // 按GID分组
    Map<String, List<Document>> gidDocMap = allDocuments.stream()
            .collect(Collectors.groupingBy(doc -> doc.getString("gid")));

    return gidDocMap;
}
```

## 性能优化效果

### 1. 数据库查询次数优化

**优化前：**
- 1000个GID = 1000次数据库查询
- 每次查询都需要建立连接、执行查询、返回结果

**优化后：**
- 1000个GID，批量大小100 = 10次数据库查询
- 查询次数减少90%

### 2. 内存使用优化

**避免极端情况：**
- **单个查询**：频繁的数据库交互，性能低下
- **全量查询**：一次性查询所有GID，可能导致内存溢出
- **批量查询**：平衡性能和内存使用，可配置调优

### 3. 处理时间优化

**理论性能提升：**
- 减少网络往返次数：从N次减少到N/batchSize次
- 减少数据库连接开销
- 提高查询效率：IN查询比多个单独查询更高效

## 技术实现细节

### 1. 分批处理逻辑

```java
for (int i = 0; i < gidList.size(); i += batchSize) {
    int endIndex = Math.min(i + batchSize, gidList.size());
    List<String> gidBatch = gidList.subList(i, endIndex);
    // 处理当前批次
}
```

### 2. 结果分组映射

```java
// 使用Stream API按GID分组
Map<String, List<Document>> gidDocMap = allDocuments.stream()
        .collect(Collectors.groupingBy(doc -> doc.getString("gid")));
```

### 3. 异常处理

```java
try {
    // 批量查询和处理
} catch (Exception e) {
    log.error("Failed to process GID batch. startIndex={}, endIndex={}", i, endIndex, e);
    dedupResult.setFailed(dedupResult.getFailed() + gidBatch.size());
}
```

## 配置建议

### 1. 批量大小选择

**推荐配置：**
- **小数据量**（< 1000 GID）：batchSize = 50-100
- **中等数据量**（1000-10000 GID）：batchSize = 100-200
- **大数据量**（> 10000 GID）：batchSize = 200-500

**考虑因素：**
- 数据库性能和连接池大小
- 应用服务器内存容量
- 网络延迟和带宽

### 2. 监控指标

**关键指标：**
- 批量查询耗时
- 内存使用情况
- 数据库连接池状态
- 整体处理时间

## 向后兼容性

### 1. 保留原有方法

```java
// 保留单个GID查询方法作为备用
private List<Document> findDocsByGid(MongoTemplate mongoTemplate, String gid, String bucketCode)
```

### 2. 配置默认值

- 默认批量大小：100
- 如果配置为1，等同于原有的单个查询方式
- 如果配置过大，系统会自动处理内存和性能问题

## 总结

这次优化通过引入可配置的批量查询机制，在保持功能完整性的同时，显著提升了去重处理的性能：

1. **性能提升**：数据库查询次数减少90%以上
2. **内存安全**：避免全量查询导致的内存溢出
3. **可配置性**：支持根据实际环境调优批量大小
4. **向后兼容**：保留原有功能，平滑升级

通过这种分批处理的方式，既解决了单个查询的性能问题，又避免了全量查询的内存风险，是一个平衡性能和资源使用的最佳实践。
